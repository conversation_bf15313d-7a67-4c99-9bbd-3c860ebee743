<?php

return [

    // 控制是否可見
    'article_visible' => true,
    'banner_visible' => true,
    'contact_visible' => true,
    'faq_visible' => true,
    'product_visible' => true,
    'brand_visible' => true,
    'customer_visible' => true,
    'client_visible' => true,

    // 控制熱門與最新的數量上限
    'article_hottest_limit' => 6,
    'product_hottest_limit' => 6,
    'product_category_hottest_limit' => 6,
    'article_newest_limit' => 6,
    'product_newest_limit' => 6,
    'product_category_newest_limit' => 6,

    // 是否讓文章與產品共享標籤
    'share_tag' => true,
    // 產品有哪些 type
    'product_types' => ['product', 'service'],
    // 是否自動產生壓縮後的圖片
    'image_variants' => true,
    // 信件中所顯示的店家名稱
    'mail_name' => '預設店家',
    // 控制後台有哪些語系
    'languages' => ['zh_TW', 'en'],
    // App 代號
    'app' => env('DB_DATABASE', 'base'),


    // 使用者最高權限帳號與系統管理員最高權限帳號
    'admin' => [
        'app_super_admin_account' => '<EMAIL>',
        'app_super_admin_password' => 'admin',
        'app_hidden_super_admin_account' => '<EMAIL>',
        'app_hidden_super_admin_password' => 'admin',
    ],
    // 預設 B2B 企業客戶帳號
    'client' => [
        'app_super_client_account' => '<EMAIL>',
        'app_super_client_password' => 'admin',
    ],
    // 預設 B2C 客戶帳號
    'customer' => [

        'app_super_customer_account' => '<EMAIL>',
        'app_super_customer_password' => 'admin',
    ],

    // 模型觀察者設定
    'observer_class' => \Stephenchenorg\BaseFilamentPlugin\Observers\AdminFieldsObserver::class,

    // 額外需要觀察的模型
    'additional_observable_models' => [
        [
            'path' => 'app/Models',
            'namespace' => 'App\\Models\\',
        ],
    ],

    // 模型配置
    'models' => [
        'ClientModel' => \Stephenchenorg\BaseFilamentPlugin\Models\Client::class,
        'CustomerModel' => \Stephenchenorg\BaseFilamentPlugin\Models\Customer::class,
    ],


    // 可以覆蓋或修改這個數組來控制要註冊的 Resources
    'resources' => [
        'ActivityLog' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ActivityLogResource::class,
        'Admin' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\AdminResource::class,
        'Article' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleResource::class,
        'ArticleCategory' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ArticleCategoryResource::class,
        'Banner' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BannerResource::class,
        'Brand' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\BrandResource::class,
        'Client' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ClientResource::class,
        'Contact' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ContactResource::class,
        'Customer' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\CustomerResource::class,
        'Faq' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqResource::class,
        'FaqCategory' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\FaqCategoryResource::class,
        'Page' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PageResource::class,
        'Permission' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\PermissionResource::class,
        'Product' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductResource::class,
        'ProductCategory' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource::class,
        'ProductSpecification' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\ProductSpecificationResource::class,
        'RewardCategory' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource::class,
        'RewardRecord' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource::class,
        'Role' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RoleResource::class,
        'Tag' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\TagResource::class,
        'Order' => \Stephenchenorg\BaseFilamentPlugin\Filament\Resources\OrderResource::class,
    ],

    // 可以覆蓋或修改這個數組來控制要執行的 Seeders
    'seeders' => [
        'Permission' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PermissionSeeder::class,
        'Role' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\RoleSeeder::class,
        'Cities' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CitiesSeeder::class,
        'Zone' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ZoneSeeder::class,
        'Admin' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\AdminSeeder::class,
        'Customer' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CustomerSeeder::class,
        'Client' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ClientSeeder::class,
        'Tag' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\TagSeeder::class,
        'Article' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ArticleSeeder::class,
        'Banner' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\BannerSeeder::class,
        'Company' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\CompanySeeder::class,
        'Contact' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ContactSeeder::class,
        'Faq' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\FaqSeeder::class,
        'Page' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\PageSeeder::class,
        'Product' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ProductSeeder::class,
        'System' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\SystemSeeder::class,
        'Order' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\OrderSeeder::class,
        'ShippingMethod' => \Stephenchenorg\BaseFilamentPlugin\Database\Seeders\ShippingMethodSeeder::class,
    ],

    // 可以覆蓋或修改這個數組來控制要註冊的 Pages
    'pages' => [
        'CompanySetting' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\CompanySettingPage::class,
        'SystemSetting' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\SystemSettingPage::class,
        'MyPage' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\MyPage::class,
        'ShippingMethodSetting' => \Stephenchenorg\BaseFilamentPlugin\Filament\Pages\ShippingMethodSettingPage::class,
    ],

    // 可以覆蓋或修改這個數組來控制要註冊的 Widgets
    'widgets' => [
        'Article' => \Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ArticleWidget::class,
        'Contact' => \Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ContactWidget::class,
        'Product' => \Stephenchenorg\BaseFilamentPlugin\Filament\Widgets\ProductWidget::class,
    ],


    // 控制金流平台
    'payment_gateway' => 'ecpay',
    // 接受的付款方式
    'accept_payment_methods' => ['NONE', 'ALL', 'CREDIT_CARD', 'ATM', 'WEB_ATM', 'CVS', 'BARCODE'],
    // 接受的發票方式
    'accept_invoice_methods' => ['none', 'duplicate', 'triplicate', 'mobile_barcode', 'citizen_card', 'donation'],
    // 三聯發票是否需要加稅
    'triplicate_tax' => true,
    // 接受的運送方式
    'accept_shipping_methods' => ['none', 'delivery', 'ok_mart', 'seven_eleven', 'family_mart', 'hi_life'],
    // 金流模式
    'payment_mode' => 'development',
    // 付款成功轉回的前端網址
    'order_result_url' => 'http://localhost:3000/result',
    // 是否使用預設的金流路由
    'use_default_payment_routes' => true,

];
