<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Models;

use Asantibanez\LaravelEloquentStateMachines\Traits\HasStateMachines;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Stephenchenorg\BaseFilamentPlugin\Database\Factories\OrderFactory;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Events\OrderPaymentStatusChanged;
use Stephenchenorg\BaseFilamentPlugin\Events\OrderShippingStatusChanged;
use Stephenchenorg\BaseFilamentPlugin\Events\OrderStatusChanged;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLogActivity;
use Stephenchenorg\BaseFilamentPlugin\StateMachines\OrderStateMachine;
use Stephenchenorg\BaseFilamentPlugin\StateMachines\PaymentStateMachine;
use Stephenchenorg\BaseFilamentPlugin\StateMachines\ShippingStateMachine;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\Model\CCTraitModelAdmin;

class Order extends Model
{
    use HasFactory;
    use CCTraitModelAdmin;
    use LogsActivity;
    use HasStateMachines;

    /**
     * @var array
     */
    protected $guarded = [];

    /**
     * State machines configuration
     *
     * @var array
     */
    protected $stateMachines = [
        'payment_status' => PaymentStateMachine::class,
        'shipping_status' => ShippingStateMachine::class,
        'status' => OrderStateMachine::class,
    ];

    /**
     * @var array
     */
    protected $casts = [
        'payment_method' => EnumPaymentMethod::class,
        'shipping_method' => EnumShippingMethod::class,
        'invoice_method' => EnumInvoiceMethod::class,
        'expire_date' => 'datetime',
    ];

    /**
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();

        static::updated(function ($order) {
            // 監聽 shipping_status 變更
            if ($order->isDirty('shipping_status')) {
                OrderShippingStatusChanged::dispatch($order);
            }

            // 監聽 payment_status 變更
            if ($order->isDirty('payment_status')) {
                OrderPaymentStatusChanged::dispatch($order);
            }

            // 監聽 status 變更
            if ($order->isDirty('status')) {
                OrderStatusChanged::dispatch($order);
            }
        });
    }

    /**
     * 訂單所有者關聯 - 使用 morphTo 關聯
     */
    public function orderable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Define the relationship.
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Define the StoreAddress.
     */
    public function storeAddress(): BelongsTo
    {
        return $this->belongsTo(StoreAddress::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return ServiceLogActivity::getDefaultOptions()
            ->useLogName('訂單');
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    protected static function newFactory(): \Stephenchenorg\BaseFilamentPlugin\Database\Factories\OrderFactory
    {
        return OrderFactory::new();
    }

    public function scopeVisibleTo($query)
    {
        $orderableType = null;
        $orderableId = null;

        if (auth()->guard('clients')->check()) {
            $client = auth()->guard('clients')->user();
            $orderableType = get_class($client);
            $orderableId = $client->id;
        }
        if (auth()->guard('customers')->check()) {
            $customer = auth()->guard('customers')->user();
            $orderableType = get_class($customer);
            $orderableId = $customer->id;
        }

        return $query
            ->where(function ($query) use ($orderableType, $orderableId) {
                return $query
                    // 有主單
                    ->where(function ($subQuery) use ($orderableType, $orderableId) {
                        return $subQuery
                            ->whereNotNull('orderable_type')
                            ->whereNotNull('orderable_id')
                            ->where('orderable_type', $orderableType)
                            ->where('orderable_id', $orderableId);
                    })
                    // 或無主單
                    ->orWhere(function ($subQuery) use ($orderableType, $orderableId) {
                        return $subQuery
                            ->whereNull('orderable_type')
                            ->whereNull('orderable_id')
                            ->where('phone', auth()->user()->phone ?? null);
                    });
            });
    }

}
