<?php

namespace <PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Order;

use Exception;
use Illuminate\Validation\Rule;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Query;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;

class CalculateCartQuery extends Query
{
    protected $attributes = [
        'name' => EnumNames::CalculateCart,
        'description' => 'Calculate cart amounts including tax and shipping',
    ];

    /**
     * @var ServiceOrder
     */
    private ServiceOrder $serviceOrder;

    /**
     * @param ServiceOrder $serviceOrder
     */
    public function __construct(ServiceOrder $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::OrderAmount);
    }

    public function args(): array
    {
        return [
            // 購物車項目
            'items' => [
                'name' => 'items',
                'type' => Type::listOf(GraphQL::type('OrderItemInput')),
                'rules' => ['required', 'array', 'min:1', 'max:15'],
            ],

            // 發票方式 (必填)
            'invoice_method' => [
                'name' => 'invoice_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumInvoiceMethod::getAvailableMethodValues())
                ],
            ],

            // 運送方式 (必填)
            'shipping_method' => [
                'name' => 'shipping_method',
                'type' => Type::string(),
                'rules' => [
                    'required',
                    Rule::in(EnumShippingMethod::getAvailableMethodValues())
                ],
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args): array
    {
        $amounts = $this->serviceOrder->calculateOrderAmounts(
            $args['items'],
            $args['shipping_method'],
            $args['invoice_method']
        );

        return [
            'item_amount' => $amounts['item_amount'],
            'tax' => $amounts['tax'],
            'total_amount_untaxed' => $amounts['total_amount_untaxed'],
            'total_amount_taxed' => $amounts['total_amount_taxed'],
            'shipping_cost' => $amounts['shipping_cost'],
        ];
    }
}
