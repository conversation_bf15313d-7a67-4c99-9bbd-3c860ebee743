<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Queries\Order;

use Exception;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Query;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\Traits\HasTranslation;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder;

class OrderQuery extends Query
{
    use HasTranslation;

    protected $attributes = [
        'name' => EnumNames::Order,
        'description' => 'Get a single order by order key',
    ];

    /**
     * @var ServiceOrder
     */
    public ServiceOrder $serviceOrder;

    /**
     * @param ServiceOrder $service
     */
    public function __construct(ServiceOrder $service)
    {
        $this->serviceOrder = $service;
    }

    public function type(): Type
    {
        return GraphQL::type(EnumNames::Order);
    }

    public function args(): array
    {
        return [
            'order_key' => [
                'name' => 'order_key',
                'type' => Type::string(),
                'rules' => ['required', 'string'],
                'description' => 'The order key to search for',
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function resolve($root, $args)
    {
        $query = Order::query();

        $query = $this->serviceOrder->validateQuery($query);

        return $query->where('order_key', $args['order_key'])->firstOrFail();
    }
}
