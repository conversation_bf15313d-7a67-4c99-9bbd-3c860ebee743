<?php

namespace Stephenchenorg\BaseFilamentPlugin\GraphQL\Types\Order;

use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Type as GraphQLType;
use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;

class OrderAmountType extends GraphQLType
{
    protected $attributes = [
        'name' => EnumNames::OrderAmount,
        'description' => 'Result of cart calculation with amount details',
    ];

    public function fields(): array
    {
        return [
            'item_amount' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'The total amount of all items',
            ],
            'tax' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'The tax amount (difference between taxed and untaxed)',
            ],
            'total_amount_untaxed' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'The total amount without tax',
            ],
            'total_amount_taxed' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'The total amount with tax',
            ],
            'shipping_cost' => [
                'type' => Type::nonNull(Type::float()),
                'description' => 'The shipping cost',
            ],
        ];
    }
}
