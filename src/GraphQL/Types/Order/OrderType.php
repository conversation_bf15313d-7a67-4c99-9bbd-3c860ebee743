<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\GraphQL\Types\Order;

use Stephenchenorg\BaseFilamentPlugin\GraphQL\EnumNames;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use GraphQL\Type\Definition\Type;
use Rebing\GraphQL\Support\Facades\GraphQL;
use Rebing\GraphQL\Support\Type as GraphQLType;

class OrderType extends GraphQLType
{
    protected $attributes = [
        'name'        => EnumNames::Order,
        'description' => 'A type that represents an order',
        'model'       => Order::class,
    ];

    public function fields(): array
    {
        return [
            'id' => [
                'type'        => Type::nonNull(Type::int()),
                'description' => 'The ID of the order',
            ],

            // 關聯會員資訊
            'orderable_type' => [
                'type'        => Type::string(),
                'description' => 'The orderable type (Customer or Client)',
            ],
            'orderable_id' => [
                'type'        => Type::int(),
                'description' => 'The orderable ID',
            ],

            // 訂購人資訊
            'name' => [
                'type'        => Type::string(),
                'description' => 'The customer name',
            ],
            'phone' => [
                'type'        => Type::string(),
                'description' => 'The customer phone',
            ],
            'email' => [
                'type'        => Type::string(),
                'description' => 'The customer email',
            ],

            // 金額資訊
            'order_key' => [
                'type'        => Type::string(),
                'description' => 'The order key',
            ],
            'item_amount' => [
                'type'        => Type::float(),
                'description' => 'The item amount of the order',
            ],
            'shipping_cost' => [
                'type'        => Type::float(),
                'description' => 'The shipping cost of the order',
            ],
            'total_amount_untaxed' => [
                'type'        => Type::float(),
                'description' => 'The untaxed total amount of the order',
            ],
            'total_amount_taxed' => [
                'type'        => Type::float(),
                'description' => 'The taxed total amount of the order',
            ],
            'tax' => [
                'type'        => Type::float(),
                'description' => 'The tax amount of the order',
                'resolve' => function ($root) {
                    return $root->total_amount_taxed - $root->total_amount_untaxed;
                },
            ],
            // 付款與運送資訊
            'payment_method' => [
                'type'        => Type::string(),
                'description' => 'The payment method',
                'resolve' => function ($root) {
                    return $root->payment_method->value;
                },
            ],
            'payment_status' => [
                'type'        => Type::string(),
                'description' => 'The payment status',
            ],
            'shipping_method' => [
                'type'        => Type::string(),
                'description' => 'The shipping method',
                'resolve' => function ($root) {
                    return $root->shipping_method->value;
                },
            ],
            'shipping_status' => [
                'type'        => Type::string(),
                'description' => 'The shipping status',
            ],
            'status' => [
                'type'        => Type::string(),
                'description' => 'The order status (pending, canceled, completed)',
            ],

            // 地址資訊
            'store_address_id' => [
                'type'        => Type::int(),
                'description' => 'The store address ID',
            ],
            'country_code' => [
                'type'        => Type::string(),
                'description' => 'The country code',
            ],
            'state' => [
                'type'        => Type::string(),
                'description' => 'The state/province',
            ],
            'city' => [
                'type'        => Type::string(),
                'description' => 'The city',
            ],
            'district' => [
                'type'        => Type::string(),
                'description' => 'The district',
            ],
            'postal_code' => [
                'type'        => Type::string(),
                'description' => 'The postal code',
            ],
            'address_line1' => [
                'type'        => Type::string(),
                'description' => 'The address line 1',
            ],
            'address_line2' => [
                'type'        => Type::string(),
                'description' => 'The address line 2',
            ],

            // 發票資訊
            'invoice_method' => [
                'type'        => Type::string(),
                'description' => 'The invoice method',
                'resolve' => function ($root) {
                    return $root->invoice_method->value;
                },
            ],
            'carrier_value' => [
                'type'        => Type::string(),
                'description' => 'The carrier value for invoice',
            ],
            'invoice_address' => [
                'type'        => Type::string(),
                'description' => 'The invoice address',
            ],
            'vat' => [
                'type'        => Type::string(),
                'description' => 'The VAT number',
            ],
            'invoice_title' => [
                'type'        => Type::string(),
                'description' => 'The company name',
            ],

            // 其他資訊
            'payload' => [
                'type'        => Type::string(),
                'description' => 'The payment gateway payload (JSON)',
                'resolve' => function ($root) {
                    return $root->payload ? json_encode($root->payload) : null;
                },
            ],
            'remark' => [
                'type'        => Type::string(),
                'description' => 'The order remark',
            ],

            // CVS/BARCODE 付款資訊
            'bank_code' => [
                'type'        => Type::string(),
                'description' => 'The bank code for payment (3 digits)',
            ],
            'v_account' => [
                'type'        => Type::string(),
                'description' => 'The virtual account number (16 digits)',
            ],
            'payment_no' => [
                'type'        => Type::string(),
                'description' => 'The payment code for CVS (14 digits, empty for BARCODE)',
            ],
            'expire_date' => [
                'type'        => Type::string(),
                'description' => 'The payment expiration date (yyyy/MM/dd HH:mm:ss)',
            ],
            'barcode1' => [
                'type'        => Type::string(),
                'description' => 'The first barcode segment (20 digits, empty for CVS)',
            ],
            'barcode2' => [
                'type'        => Type::string(),
                'description' => 'The second barcode segment (20 digits, empty for CVS)',
            ],
            'barcode3' => [
                'type'        => Type::string(),
                'description' => 'The third barcode segment (20 digits, empty for CVS)',
            ],

            // 時間戳記
            'created_at' => [
                'type'        => Type::string(),
                'description' => 'The creation date',
            ],
            'updated_at' => [
                'type'        => Type::string(),
                'description' => 'The last update date',
            ],

            // 關聯資料
            'items' => [
                'type'        => Type::listOf(GraphQL::type(EnumNames::OrderItem)),
                'description' => 'The order items',
            ],
        ];
    }
}
