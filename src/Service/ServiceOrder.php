<?php

namespace Stephen<PERSON>org\BaseFilamentPlugin\Service;

use Exception;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumInvoiceMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingMethod;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\Client;
use Stephenchenorg\BaseFilamentPlugin\Models\Customer;
use Stephenchenorg\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\OrderItem;
use Stephenchenorg\BaseFilamentPlugin\Models\Product;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;
use Stephenchenorg\BaseFilamentPlugin\Models\ShippingMethod;

/**
 *  訂單服務
 */
class ServiceOrder
{


    /**
     * 建立訂單
     *
     * @param array $data 包含完整的結帳資料
     * @return Order 新建立的訂單物件
     * @throws Exception 如果建立訂單失敗，會拋出異常
     */
    public function createOrder(array $data): Order
    {
        DB::beginTransaction();

        try {

            // 檢查物流方式
            $this->checkShippingMethod($data['items'],$data['shipping_method']);

            // 檢查庫存並鎖定
            $this->checkInventory($data['items']);

            // 檢查登入狀態並取得 orderable 資訊
            $orderableData = $this->getOrderableData();

            // 計算訂單金額
            $orderData = $this->calculateOrderAmounts($data['items'], $data['shipping_method'], $data['invoice_method']);

            // 產生訂單編號
            $orderKey = $this->generateOrderKey();

            // 建立訂單
            $order = Order::create([
                'orderable_type' => $orderableData['type'],
                'orderable_id' => $orderableData['id'],
                'order_key' => $orderKey,
                'total_amount_untaxed' => $orderData['total_amount_untaxed'],
                'total_amount_taxed' => $orderData['total_amount_taxed'],
                'shipping_cost' => $orderData['shipping_cost'],
                'item_amount' => $orderData['item_amount'],
                'payment_method' => $data['payment_method'],
                'payment_status' => EnumPaymentStatus::UNPAID->value,
                'shipping_method' => $data['shipping_method'],
                'shipping_status' => EnumShippingStatus::UNSHIPPED->value,
                'status' => EnumOrderStatus::PENDING->value,
                'name' => $data['name'],
                'phone' => $data['phone'],
                'email' => $data['email'],
                'payment_gateway' => config('cs.payment_gateway') ?? null,

                // 地址資訊
                'store_address_id' => $data['store_address_id'] ?? null,
                'country_code' => $data['country_code'] ?? null,
                'state' => $data['state'] ?? null,
                'city' => $data['city'] ?? null,
                'district' => $data['district'] ?? null,
                'postal_code' => $data['postal_code'] ?? null,
                'address_line1' => $data['address_line1'] ?? null,
                'address_line2' => $data['address_line2'] ?? null,

                // 發票資訊
                'invoice_method' => $data['invoice_method'],
                'carrier_value' => $data['carrier_value'] ?? null,
                'invoice_address' => $data['invoice_address'] ?? null,
                'vat' => $data['vat'] ?? null,
                'invoice_title' => $data['invoice_title'] ?? null,
            ]);

            // 建立訂單項目
            $this->createOrderItems($order, $data['items']);

            DB::commit();
            return $order->load('items');

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 取消訂單
     *
     * @param Order $order 要取消的訂單
     * @return Order 更新後的訂單物件
     * @throws Exception 如果取消訂單失敗，會拋出異常
     */
    public function cancelOrder(Order $order): Order
    {
        DB::beginTransaction();

        try {
            // 使用 state machine 將訂單狀態改為取消
            $order->status()->transitionTo(EnumOrderStatus::CANCELED->value);
            // 釋放產品預留量
            $this->releaseProductReservations($order);

            DB::commit();
            return $order->fresh();

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 設定訂單付款狀態
     *
     * @param Order $order 訂單物件
     * @param string $status 目標狀態
     * @return Order 更新後的訂單物件
     * @throws Exception 如果狀態轉換失敗
     */
    public function setOrderPaymentStatus(Order $order, string $status): Order
    {
        try {
            DB::beginTransaction();

            // 檢查是否可以轉換到該狀態
            if (!$order->paymentStatus()->canBe($status)) {
                throw new Exception("無法從當前付款狀態 '{$order->payment_status}' 轉換到 '{$status}'");
            }

            // 使用狀態機轉換狀態
            $order->paymentStatus()->transitionTo($status);
            $order->refresh();


            if ($order->payment_status === EnumPaymentStatus::PAID->value &&
                $order->shipping_status === EnumShippingStatus::COMPLETED->value
            ) {
                // 使用狀態機將訂單狀態改為完成
                $order->status()->transitionTo(EnumOrderStatus::COMPLETED->value);
            }

            if ($order->payment_status === EnumPaymentStatus::FAILED->value) {
                // 失敗
                $order->status()->transitionTo(EnumOrderStatus::FAILED->value);
            }

            // 重新載入訂單以獲取最新狀態
            $order->refresh();

            DB::commit();

            return $order;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("設定訂單付款狀態失敗: {$e->getMessage()}");
        }
    }

    /**
     * 設定訂單配送狀態
     *
     * @param Order $order 訂單物件
     * @param string $status 目標狀態
     * @return Order 更新後的訂單物件
     * @throws Exception 如果狀態轉換失敗
     */
    public function setOrderShippingStatus(Order $order, string $status): Order
    {
        try {
            DB::beginTransaction();

            // 檢查是否可以轉換到該狀態
            if (!$order->shipping_status()->canBe($status)) {
                throw new Exception("無法從當前配送狀態 '{$order->shipping_status}' 轉換到 '{$status}'");
            }

            // 使用狀態機轉換狀態
            $order->shipping_status()->transitionTo($status);
            $order->refresh();

            if ($order->shipping_status === EnumShippingStatus::SHIPPED->value) {
                // 扣除庫存
                $this->deductInventory($order);
                // 釋放預留量
                $this->releaseProductReservations($order);
            }
            if ($order->shipping_status === EnumShippingStatus::COMPLETED->value &&
                $order->payment_status === EnumPaymentStatus::PAID->value
            ) {
                // 使用狀態機將訂單狀態改為完成
                $order->status()->transitionTo(EnumOrderStatus::COMPLETED->value);
            }
            if ($order->shipping_status === EnumShippingStatus::PICKUP_FAILED->value) {
                // 失敗
                $order->status()->transitionTo(EnumOrderStatus::FAILED->value);
            }

            // 重新載入訂單以獲取最新狀態
            $order->refresh();

            DB::commit();

            return $order;
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("設定訂單配送狀態失敗: {$e->getMessage()}");
        }
    }


    /**
     * 扣除庫存
     *
     * @param Order $order 訂單物件
     * @throws Exception 如果庫存不足
     */
    public function deductInventory(Order $order): void
    {
        try {
            DB::beginTransaction();

            // 載入訂單項目
            $order->load('items');

            foreach ($order->items as $orderItem) {
                $productSpecificationId = $orderItem->product_specification_id;
                $quantity = $orderItem->quantity;

                // 使用 lockForUpdate 鎖定產品規格記錄
                $specification = ProductSpecification::query()
                    ->where('id', $productSpecificationId)
                    ->lockForUpdate()
                    ->first();

                if (!$specification) {
                    throw new Exception("產品規格不存在: ID {$productSpecificationId}");
                }

                // 檢查庫存是否足夠
                if ($specification->inventory < $quantity) {
                    throw new Exception("產品規格 ID {$productSpecificationId} 庫存不足，當前庫存: {$specification->inventory}，需要: {$quantity}");
                }

                // 扣除實際庫存
                $specification->decrement('inventory', $quantity);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("扣除庫存失敗: {$e->getMessage()}");
        }
    }


    /**
     * 釋放訂單的產品預留量
     *
     * @param Order $order 訂單物件
     */
    private function releaseProductReservations(Order $order): void
    {
        // 載入訂單項目
        $order->load('items');

        foreach ($order->items as $orderItem) {
            // 查找現有的預留記錄
            $reservingItem = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $orderItem->product_specification_id)
                ->first();
            if ($reservingItem) {
                // 減少預留量
                $newQuantity = $reservingItem->quantity - $orderItem->quantity;
                if ($newQuantity <= 0) {
                    // 如果預留量歸零或負數，刪除記錄
                    $reservingItem->delete();
                } else {
                    // 更新預留量
                    $reservingItem->update(['quantity' => $newQuantity]);
                }
            }
        }
    }


    public function checkInventory(array $items): void
    {
        $errors = [];

        foreach ($items as $index => $item) {
            $productSpecificationId = $item['product_specification_id'];
            $requestedQuantity = $item['quantity'];

            // 使用 lockForUpdate 鎖定產品規格記錄
            $specification = ProductSpecification::query()
                ->where('id', $productSpecificationId)
                ->lockForUpdate()
                ->first();

            if (!$specification) {
                $errors["items.$index.product_specification_id"] = ['產品規格不存在'];
                continue;
            }

            // 計算已預留的數量
            $reservedQuantity = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $productSpecificationId)
                ->sum('quantity');

            // 計算可用庫存
            $availableInventory = $specification->inventory - $reservedQuantity;

            // 檢查庫存是否足夠
            if ($availableInventory < $requestedQuantity) {
                $errors["items.$index.product_specification_id"] = ['庫存不足'];
            }
        }

        // 如果有錯誤，拋出 ValidationException
        if (!empty($errors)) {
            throw ValidationException::withMessages($errors);
        }

    }

    public function checkShippingMethod(array $items,string $shippingMethod): void
    {

        // 檢查 env
        $availableShippingMethodValues = EnumShippingMethod::getAvailableMethodValues();


        if(!in_array($shippingMethod, $availableShippingMethodValues)) {
            throw ValidationException::withMessages([
                'shipping_method' => "不支援的物流方式 {$shippingMethod}",
            ]);
        }


        // 檢查 資料庫預設運費資料
        $methodModel = ShippingMethod::query()->where('type','=',$shippingMethod)->first();

        if(empty($methodModel)){
            throw ValidationException::withMessages(['shipping_method' => '不支援的物流方式']);
        }


        $specIds = array_map(function ($item) {
            return $item['product_specification_id'];
        }, $items);


        // 此次訂單所涉及到的『產品』

        $products = Product::query()
            ->with([
                'shippingMethods'
            ])
            ->whereHas('specifications', function ($query) use ($specIds) {
                $query->whereIn('id', $specIds);
            })
            ->get();


        // 檢查所有『單品設定』
        foreach ($products as $product) {

            $productShippingMethod = $product->shippingMethods->where('type','=',$shippingMethod)->first();
            if(empty($productShippingMethod)) continue;

            if($productShippingMethod->pivot->status == 0)
            {
                throw ValidationException::withMessages([
                    'shipping_method' => "不支援的物流方式 {$shippingMethod}",
                ]);
            }
        }


    }



    /**
     * 檢查登入狀態並取得 orderable 資訊
     */
    protected function getOrderableData(): array
    {
        // 檢查 customers guard
        if (Auth::guard('customers')->check()) {
            return [
                'type' => Customer::class,
                'id' => Auth::guard('customers')->id(),
            ];
        }

        // 檢查 clients guard
        if (Auth::guard('clients')->check()) {
            return [
                'type' => Client::class,
                'id' => Auth::guard('clients')->id(),
            ];
        }

        // 未登入狀態，返回 null
        return [
            'type' => null,
            'id' => null,
        ];
    }

    /**
     * 計算訂單金額
     */
    public function calculateOrderAmounts(array $items, string $shippingMethod, string $invoiceMethod): array
    {
        $itemAmount = 0;

        foreach ($items as $item) {
            $specification = ProductSpecification::findOrFail($item['product_specification_id']);
            $itemAmount += $specification->selling_price * $item['quantity'];
        }

        $shippingCost = $this->getShippingCost($items, $shippingMethod);
        $totalAmountUntaxed = $itemAmount + $shippingCost;

        // 檢查是否為三聯發票且需要加稅
        $invoiceMethodEnum = EnumInvoiceMethod::tryFrom($invoiceMethod);
        $shouldApplyTax = $invoiceMethodEnum &&
            $invoiceMethodEnum->isTriplicate() &&
            config('cs.triplicate_tax', true);

        $totalAmountTaxed = $shouldApplyTax ?
            round($totalAmountUntaxed * 1.05, 2) :
            $totalAmountUntaxed;

        $tax = $totalAmountTaxed - $totalAmountUntaxed;

        return [
            'item_amount' => $itemAmount,
            'shipping_cost' => $shippingCost,
            'total_amount_untaxed' => $totalAmountUntaxed,
            'total_amount_taxed' => $totalAmountTaxed,
            'tax' => $tax,
        ];
    }

    /**
     * 產生訂單編號
     */
    protected function generateOrderKey(): string
    {
        $date = date('md');
        $unique = uniqid();
        return $date . $unique;
    }


    /**
     * 建立訂單項目
     */
    protected function createOrderItems(Order $order, array $items): void
    {
        foreach ($items as $item) {

            $lang = App::getLocale() ?? ServiceLanguage::getDefaultLanguage();
            $specification = ProductSpecification::query()->findOrFail($item['product_specification_id']);
            $translationTitle = $specification->product->translations()->where('lang', $lang)->first()->title;
            $title = $translationTitle;

            if (!empty($specification->combination_key)) {
                $combinationName = ServiceProductSpecification::getCombinationName($specification->combination_key, $lang);
                if ($combinationName) {
                    $title .= ' (' . $combinationName . ')';
                }
            }


            OrderItem::query()->create([
                'order_id' => $order->id,
                'product_specification_id' => $item['product_specification_id'],
                'title' => $title,
                'quantity' => $item['quantity'],
                'unit_price' => $specification->selling_price,
                'total_amount' => $specification->selling_price * $item['quantity'],
            ]);

            // 建立預留記錄
            $productSpecificationId = $item['product_specification_id'];
            $quantity = $item['quantity'];

            // 查找現有的預留記錄
            $reservingItem = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $productSpecificationId)
                ->first();

            if ($reservingItem) {
                // 如果已存在，增加預留量
                $reservingItem->increment('quantity', $quantity);
            } else {
                // 如果不存在，建立新記錄
                ProductSpecificationReservingItem::query()->create([
                    'product_specification_id' => $productSpecificationId,
                    'quantity' => $quantity
                ]);
            }

        }
    }

    /**
     * 計算運費
     */
    public function getShippingCost(array $items, string $shippingMethod): float
    {
        $specIds = array_map(function ($item) {
            return $item['product_specification_id'];
        }, $items);

        $specIdToProductId = ProductSpecification::query()
            ->whereIn('id', $specIds)
            ->get()
            ->pluck('product_id', 'id')
            ->toArray();

        // 各個『產品』的購買數量
        $quantityKeyByProductId = collect($items)->reduce(function ($carry, $item) use ($specIdToProductId) {
            $specId = $item['product_specification_id'];
            $productId = $specIdToProductId[$specId];
            if (empty($carry[$productId])) $carry[$productId] = 0;
            $carry[$productId] += $item['quantity'];
            return $carry;
        }, []);



        // 此次訂單所涉及到的『產品』
        $products = Product::query()
            ->with([
                'shippingMethods'
            ])
            ->whereHas('specifications', function ($query) use ($specIds) {
                $query->whereIn('id', $specIds);
            })
            ->get();


        // 不同產品運費要疊加的產品
        $combineProducts = $products->filter(function ($product) use ($shippingMethod) {
            // 先查看是否有『單品設定』
            $productShippingMethod = $product->shippingMethods->where('type', '=', $shippingMethod)->first();

            // 如果沒有『單品設定』那麼就是默認『不疊加』
            if (empty($productShippingMethod)) return false;

            // 如果有『單品設定』則依照他選擇的模式
            return $productShippingMethod->pivot->combine_different_product == 1;
        });

        $combineProductIds = $combineProducts->pluck('id')->toArray();

        $doNotCombineProducts = $products->filter(function ($product) use ($combineProductIds) {
            return !in_array($product->id, $combineProductIds);
        });

        // 開始計算運費
        $methodModel = ShippingMethod::query()->where('type','=',$shippingMethod)->first();

        $defaultCost = $methodModel->default_amount;

        $shippingCost = 0;



        foreach($combineProducts as $combineProduct) {

            // 先查看是否有『單品設定』
            $productShippingMethod = $combineProduct->shippingMethods->where('type', '=', $shippingMethod)->first();

            // 若沒有『單品設定』則使用預設的運費
            if (empty($productShippingMethod)){
                $shippingCost += $defaultCost;
                continue;
            };

            // 如果有『單品設定』但是相同商品不需要疊加
            if($productShippingMethod->pivot->combine_same_product == 0)
            {
                $shippingCost += $productShippingMethod->pivot->amount;
                continue;
            }

            // 如果有『單品設定』並且相同商品運費要疊加
            $shippingCost += $productShippingMethod->pivot->amount * $quantityKeyByProductId[$combineProduct->id];
        }

        foreach($doNotCombineProducts as $doNotCombineProduct) {

            // 先查看是否有『單品設定』
            $productShippingMethod = $doNotCombineProduct->shippingMethods->where('type', '=', $shippingMethod)->first();

            // 若沒有『單品設定』則使用預設的運費
            if (empty($productShippingMethod)){
                $shippingCost = max($shippingCost, $defaultCost);
                continue;
            };

            // 如果有『單品設定』但是相同商品不需要疊加
            if($productShippingMethod->pivot->combine_same_product == 0)
            {
                $shippingCost = max($shippingCost, $productShippingMethod->pivot->amount);
                continue;
            }

            // 如果有『單品設定』並且相同商品運費要疊加
            $shippingCost = max($shippingCost, $productShippingMethod->pivot->amount * $quantityKeyByProductId[$doNotCombineProduct->id]);

        }


        return $shippingCost;
    }


    public function validateQuery($query)
    {
        $orderableType = null;
        $orderableId = null;

        if (auth()->guard('clients')->check()) {
            $client = auth()->guard('clients')->user();
            $orderableType = get_class($client);
            $orderableId = $client->id;
        }
        if (auth()->guard('customers')->check()) {
            $customer = auth()->guard('customers')->user();
            $orderableType = get_class($customer);
            $orderableId = $customer->id;
        }

        return $query
            ->where(function ($query) use ($orderableType, $orderableId) {
                return $query
                    // 有主單
                    ->where(function ($subQuery) use ($orderableType, $orderableId) {
                        return $subQuery
                            ->whereNotNull('orderable_type')
                            ->whereNotNull('orderable_id')
                            ->where('orderable_type', $orderableType)
                            ->where('orderable_id', $orderableId);
                    })
                    // 或無主單
                    ->orWhere(function ($subQuery) use ($orderableType, $orderableId) {
                        return $subQuery
                            ->whereNull('orderable_type')
                            ->whereNull('orderable_id')
                            ->where('phone', auth()->user()->phone ?? null);
                    });
            });

    }

}
