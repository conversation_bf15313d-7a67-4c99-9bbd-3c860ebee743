<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardCategoryResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\RewardCategory;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Stephenchenorg\BaseFilamentPlugin\Traits\CCTraitFormContent;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class RewardCategoryResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = RewardCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-gift';

    protected static ?string $navigationGroup = '獎勵管理';

    protected static ?string $label = '獎勵類別';

    protected static ?int $navigationSort = 140;

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRewardCategories::route('/'),
            'create' => Pages\CreateRewardCategory::route('/create'),
            'edit' => Pages\EditRewardCategory::route('/{record}/edit'),
            'view' => Pages\ViewRewardCategory::route('/{record}'),
        ];
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                self::getTabLanguage([
                    self::getFormTitle(),
                ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label('獎勵類別鍵值')
                    ->searchable()
                    ->sortable(),
                self::getColumnTranslation('title', true)
                    ->label('獎勵類別標題'),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10)
            ->filters([
                //
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }
}
