<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource\Widgets;

use <PERSON>chenorg\BaseFilamentPlugin\Filament\Resources\ProductCategoryResource;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductCategory;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Illuminate\Database\Eloquent\Model;
use SolutionForest\FilamentTree\Actions\DeleteAction;
use SolutionForest\FilamentTree\Actions\EditAction;
use SolutionForest\FilamentTree\Actions\ViewAction;
use SolutionForest\FilamentTree\Widgets\Tree as BaseWidget;

class ProductCategoryWidget extends BaseWidget
{
    protected static string $model = ProductCategory::class;

    protected static int $maxDepth = 2;

    protected ?string $treeTitle = '樹狀清單';

    protected bool $enableTreeTitle = true;

    public function getViewFormSchema(): array
    {
        return [
            //
        ];
    }

    // INFOLIST, CAN DELETE

    /**
     * @param  Model|null  $record
     * @return string
     */
    public function getTreeRecordTitle(?Model $record = null): string
    {
        return $record->key;
    }

    public function getNodeCollapsedState(?Model $record = null): bool
    {
        return true;
    }

    protected function getFormSchema(): array
    {
        return [
            Repeater::make('children')
                ->relationship('children') // 使用 HasMany 關聯
                ->schema([
                    TextInput::make('name')->required(),
                    // 可以根據需求添加更多欄位
                ])
                ->collapsed()  // 預設子項折疊顯示
                ->createItemButtonLabel('Add Child Category'), // 自定義按鈕名稱

        ];
    }

    /**
     * @return array
     */
    protected function getTreeActions(): array
    {
        return [
            ViewAction::make()
                ->url(fn($record) => ProductCategoryResource::getUrl('view', ['record' => $record])),
            EditAction::make()
                ->url(fn($record) => ProductCategoryResource::getUrl('edit', ['record' => $record])),
            DeleteAction::make(),
        ];
    }


    // CUSTOMIZE ICON OF EACH RECORD, CAN DELETE
    // public function getTreeRecordIcon(?\Illuminate\Database\Eloquent\Model $record = null): ?string
    // {
    //     return null;
    // }

    // CUSTOMIZE ACTION OF EACH RECORD, CAN DELETE
    // protected function getTreeActions(): array
    // {
    //     return [
    //         Action::make('helloWorld')
    //             ->action(function () {
    //                 Notification::make()->success()->title('Hello World')->send();
    //             }),
    //         // ViewAction::make(),
    //         // EditAction::make(),
    //         ActionGroup::make([
    //
    //             ViewAction::make(),
    //             EditAction::make(),
    //         ]),
    //         DeleteAction::make(),
    //     ];
    // }
    // OR OVERRIDE FOLLOWING METHODS
    //protected function hasDeleteAction(): bool
    //{
    //    return true;
    //}
    //protected function hasEditAction(): bool
    //{
    //    return true;
    //}
    //protected function hasViewAction(): bool
    //{
    //    return true;
    //}
}
