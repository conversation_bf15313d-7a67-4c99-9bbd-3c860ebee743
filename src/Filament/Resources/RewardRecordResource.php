<?php

namespace Stephenchenorg\BaseFilamentPlugin\Filament\Resources;

use Stephenchenorg\BaseFilamentPlugin\Filament\Resources\RewardRecordResource\Pages;
use Stephenchenorg\BaseFilamentPlugin\Models\RewardRecord;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceLanguage;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Filters\SelectFilter;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitAction;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitColumn;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormAdmin;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormContent;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormEditor;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormOG;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSEO;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormSort;
use Stephenchenorg\CsCoreFilamentPlugin\Traits\CCTraitFormTimestamps;

class RewardRecordResource extends Resource
{
    use CCTraitColumn;
    use CCTraitFormAdmin;
    use CCTraitFormTimestamps;
    use CCTraitFormContent;
    use CCTraitFormEditor;
    use CCTraitFormSort;
    use CCTraitFormSEO;
    use CCTraitFormOG;
    use CCTraitAction;

    protected static ?string $model = RewardRecord::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = '獎勵管理';

    protected static ?string $label = '獎勵記錄';

    protected static ?int $navigationSort = 150;

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRewardRecords::route('/'),
            'create' => Pages\CreateRewardRecord::route('/create'),
            'edit' => Pages\EditRewardRecord::route('/{record}/edit'),
            'view' => Pages\ViewRewardRecord::route('/{record}'),
        ];
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('basic_settings')
                    ->heading('基本設定')
                    ->schema([
                        Select::make('reward_category_id')
                            ->label('獎勵類別')
                            ->relationship('category', 'id')
                            ->getOptionLabelFromRecordUsing(function($record) {
                                return $record->translations
                                    ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                                    ->first()
                                    ->title;
                            })
                            ->columnSpanFull()
                            ->required(),

                        TextInput::make('rewardable_type')
                            ->label('獎勵對象類型')
                            ->required(),

                        TextInput::make('rewardable_id')
                            ->label('獎勵對象ID')
                            ->numeric()
                            ->required(),

                        TextInput::make('old_points')
                            ->label('舊點數')
                            ->numeric()
                            ->required(),

                        TextInput::make('new_points')
                            ->label('新點數')
                            ->numeric()
                            ->required(),
                    ]),

                self::getFormTimestamps(),
                self::getFormSectionAdminId(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                self::getColumnTranslation('title', false, 'category')
                    ->label('獎勵類別'),
                Tables\Columns\TextColumn::make('rewardable_type')
                    ->label('獎勵對象類型'),
                Tables\Columns\TextColumn::make('rewardable_id')
                    ->label('獎勵對象ID'),
                Tables\Columns\TextColumn::make('old_points')
                    ->label('舊點數')
                    ->numeric(),
                Tables\Columns\TextColumn::make('new_points')
                    ->label('新點數')
                    ->numeric(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('建立時間')
                    ->dateTime(),
            ])
            ->paginated([5, 10, 25, 50, 100])
            ->defaultPaginationPageOption(10)
            ->filters([
                SelectFilter::make('reward_category_id')
                    ->preload()
                    ->relationship('category','id')
                    ->getOptionLabelFromRecordUsing(function ($record) {
                        return $record->translations
                            ->where('lang', '=', ServiceLanguage::getDefaultLanguage())
                            ->first()->title;
                    })
                    ->label('獎勵類別')
                    ->searchable(),
            ])
            ->actions([
                self::getActionEdit(),
                self::getActionView(),
                self::getActionDelete(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }
}
