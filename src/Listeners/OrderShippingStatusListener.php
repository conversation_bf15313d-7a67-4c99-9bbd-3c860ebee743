<?php

namespace Stephenchenorg\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Events\OrderShippingStatusChanged;
use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumShippingStatus;
use Stephen<PERSON>org\BaseFilamentPlugin\Enum\EnumPaymentStatus;
use Stephenchenorg\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecification;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderShippingStatusListener
{
    protected ServiceOrder $serviceOrder;

    public function __construct(ServiceOrder $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    /**
     * Handle the event.
     */
    public function handle(OrderShippingStatusChanged $event): void
    {
        $order = $event->order;
        $order = $order->refresh();

        try {
            DB::beginTransaction();

            // 當配送狀態為已發貨時，扣除庫存並釋放預留量
            if ($order->shipping_status === EnumShippingStatus::SHIPPED->value) {
                $this->serviceOrder->deductInventory($order);
                $this->serviceOrder->releaseProductReservations($order);
            }

            // 當配送狀態為已完成且付款狀態為已付款時，將訂單狀態改為完成
            if ($order->shipping_status === EnumShippingStatus::COMPLETED->value &&
                $order->payment_status === EnumPaymentStatus::PAID->value
            ) {
                $order->status()->transitionTo(EnumOrderStatus::COMPLETED->value);
            }

            // 當配送狀態為無人取貨時，將訂單狀態改為失敗
            if ($order->shipping_status === EnumShippingStatus::PICKUP_FAILED->value) {
                $order->status()->transitionTo(EnumOrderStatus::FAILED->value);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("處理配送狀態變更失敗: {$e->getMessage()}");
        }
    }

}
