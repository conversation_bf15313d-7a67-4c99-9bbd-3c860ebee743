<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephen<PERSON>org\BaseFilamentPlugin\Events\OrderStatusChanged;
use Illuminate\Support\Facades\DB;
use Exception;
use Stephenchenorg\BaseFilamentPlugin\Service\ServiceOrder;

class OrderStatusListener
{
    protected ServiceOrder $serviceOrder;

    public function __construct(ServiceOrder $serviceOrder)
    {
        $this->serviceOrder = $serviceOrder;
    }

    /**
     * Handle the event.
     */
    public function handle(OrderStatusChanged $event): void
    {
        $order = $event->order;
        $order = $order->refresh();

        try {
            DB::beginTransaction();

            if($order->status === EnumOrderStatus::CANCELED->value) {
                // 釋放產品預留量
                $this->serviceOrder->releaseProductReservations($order);
            }
            if($order->status === EnumOrderStatus::FAILED->value) {
                // 釋放產品預留量
                $this->serviceOrder->releaseProductReservations($order);
            }


            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("處理訂單狀態變更失敗: {$e->getMessage()}");
        }
    }
}
