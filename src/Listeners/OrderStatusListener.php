<?php

namespace <PERSON><PERSON>org\BaseFilamentPlugin\Listeners;

use <PERSON><PERSON>org\BaseFilamentPlugin\Enum\EnumOrderStatus;
use Stephenchenorg\BaseFilamentPlugin\Events\OrderStatusChanged;
use Stephen<PERSON>org\BaseFilamentPlugin\Models\Order;
use Stephenchenorg\BaseFilamentPlugin\Models\ProductSpecificationReservingItem;
use Illuminate\Support\Facades\DB;
use Exception;

class OrderStatusListener
{
    /**
     * Handle the event.
     */
    public function handle(OrderStatusChanged $event): void
    {
        $order = $event->order;
        $order = $order->refresh();

        try {
            DB::beginTransaction();

            if($order->status === EnumOrderStatus::CANCELED->value) {
                // 釋放產品預留量
                $this->releaseProductReservations($order);
            }
            if($order->status === EnumOrderStatus::FAILED->value) {
                // 釋放產品預留量
                $this->releaseProductReservations($order);
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception("處理訂單狀態變更失敗: {$e->getMessage()}");
        }
    }

    /**
     * 釋放訂單的產品預留量
     *
     * @param Order $order 訂單物件
     */
    private function releaseProductReservations(Order $order): void
    {
        // 載入訂單項目
        $order->load('items');

        foreach ($order->items as $orderItem) {
            // 查找現有的預留記錄
            $reservingItem = ProductSpecificationReservingItem::query()
                ->where('product_specification_id', $orderItem->product_specification_id)
                ->first();

            if ($reservingItem) {
                // 減少預留量
                $newQuantity = $reservingItem->quantity - $orderItem->quantity;
                if ($newQuantity <= 0) {
                    // 如果預留量歸零或負數，刪除記錄
                    $reservingItem->delete();
                } else {
                    // 更新預留量
                    $reservingItem->update(['quantity' => $newQuantity]);
                }
            }
        }

        $order->update([
            'released' => true
        ]);
    }
}
